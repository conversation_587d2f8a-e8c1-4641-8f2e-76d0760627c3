package routes

import (
	backend_routes "med-api/backend-routes"
	"med-api/middleware"

	"github.com/canvas-tech-horizon/notelink"
)

func SetupRoutes(app *notelink.ApiNote) {
	backend_routes.LoginRoutesApi(app) //SignUP's API docs routes
	app.Use(middleware.AuthMiddleware())

	//app.Use(middleware.AuthMiddleware()) //Auth middleware for all routes below this line
	backend_routes.UserApiRoutes(app)   //User's API docs routes
	backend_routes.CourseApiRoutes(app) //Course's API docs routes
}
