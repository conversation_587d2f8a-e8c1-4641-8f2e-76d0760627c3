package models

type Exam struct {
	ID              uint   `json:"id" gorm:"primaryKey"`
	ExamName        string `json:"exam_name" binding:"required"`
	ExamDescription string `json:"exam_description"`
	PassingScore    int    `json:"passing_score" binding:"required"`
	TimeLimit       int    `json:"time_limit"`

	CourseID uint    `json:"course_id"`
	Course   Courses `json:"course" gorm:"foreignKey:CourseID"`
}
