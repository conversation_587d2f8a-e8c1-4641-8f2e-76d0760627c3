package api_documents

import (
	"med-api/controllers"
	models_api "med-api/models-api"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteUser(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/users",
		Description: "List users",
		Responses: map[string]string{
			"200": "Success",
			"401": "Unauthorized",
		},
		Handler:         controllers.GetUsers,
		Params:          []notelink.Parameter{},
		SchemasRequest:  nil,
		SchemasResponse: models_api.AllUsersRespond{},
	})
}

func NoteRouteUserID(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "GET",
		Path:        "/users/:slug",
		Description: "Get user by slug",
		Responses: map[string]string{
			"200": "Success",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.GetUserID,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Type:        "integer",
				Description: "Slug of the user to retrieve",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: models_api.UserRespond{},
	})
}

func NoteRouteUserCreate(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/users",
		Description: "Create user",
		Responses: map[string]string{
			"201": "Created",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler:         controllers.CreateUsers,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_api.CreateUser{},
		SchemasResponse: nil,
	})
}

func NoteRouteUserUpdate(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "PUT",
		Path:        "/users/:slug",
		Description: "Update user",
		Responses: map[string]string{
			"200": "Success",
			"400": "Invalid Slug",
			"500": "Internal Server Error",
		},
		Handler: controllers.UpdateUser,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path",
				Type:        "integer",
				Description: "Slug of the user to update",
				Required:    true,
			},
		},
		SchemasRequest:  models_api.UserRequest{},
		SchemasResponse: nil,
	})
}

func NoteRouteUserDelete(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "DELETE",
		Path:        "/users/:slug",
		Description: "Delete user",
		Responses: map[string]string{
			"200": "User deleted successfully",
			"404": "User not found",
			"500": "Internal Server Error",
		},
		Handler: controllers.DeleteUser,
		Params: []notelink.Parameter{
			{
				Name:        "slug",
				In:          "path", // It's a path param like /users/:id
				Type:        "integer",
				Description: "ID of the user to delete",
				Required:    true,
			},
		},
		SchemasRequest:  nil,
		SchemasResponse: nil,
	})
}
