package utils

import (
	"math/big"

	"github.com/google/uuid"
)

const base62Chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz" // used in base62Encode

func base62Encode(b []byte) string {
	num := new(big.Int).SetBytes(b)
	var result []byte
	base := big.NewInt(62)
	zero := big.NewInt(0)
	mod := new(big.Int)

	for num.Cmp(zero) > 0 {
		num.DivMod(num, base, mod)
		result = append([]byte{base62Chars[mod.Int64()]}, result...)
	}
	return string(result)
}

func GenerateUUIDSlug() string {
	u := uuid.New()
	encoded := base62Encode(u[:]) // encode the UUID
	return encoded[:20]           // take only the first 16 characters
}
