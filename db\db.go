package db

import (
	"os"

	"med-api/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

func InitDB() {
	dsn := os.Getenv("DATABASE_URL")

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic("Failed to connect to database")
	}

	DB.AutoMigrate(&models.Role{}, &models.UserInfo{},
		&models.Courses{}, &models.Lessons{}, &models.Quiz{}, &models.ContentProgress{},
		&models.ContentLesson{}, &models.Question{}, &models.QuizChoices{},
		&models.ContentType{}, &models.Certificate{}, &models.Exam{}, &models.ExamRecord{},
		&models.CoursePath{}, &models.CourseStore{}, &models.AssignPath{})

	DB.FirstOrCreate(&models.Role{}, models.Role{ID: 1, Name: "student"})
	DB.FirstOrCreate(&models.Role{}, models.Role{ID: 2, Name: "lecturer"})
	DB.FirstOrCreate(&models.Role{}, models.Role{ID: 3, Name: "admin"})
}
