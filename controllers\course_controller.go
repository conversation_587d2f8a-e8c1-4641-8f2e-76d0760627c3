package controllers

import (
	"fmt"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"

	"github.com/gofiber/fiber/v2"
)

func CreateCourse(c *fiber.Ctx) error {
	var courseRequest models_request.RequestCourse
	var user models.UserInfo

	if err := c.<PERSON>er(&courseRequest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	// Find user by lecturer slug (string)
	if err := db.DB.Where("slug = ?", courseRequest.Lecturer).First(&user).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Lecturer not found."})
	}

	userID := user.ID

	fmt.Println(user.Slug)
	fmt.Println(userID)

	var coursePicture string
	if courseRequest.CoverImage != nil {
		coursePicture = *courseRequest.CoverImage
	}

	newCourse := models.Courses{
		CourseName:        courseRequest.Title,
		CoursePicture:     coursePicture,
		CourseDescription: courseRequest.Description,
		CourseDifficulty:  courseRequest.Difficulty,
		CourseInstruction: courseRequest.ModuleDescription,
		CourseDuration:    fmt.Sprintf("%d", courseRequest.Duration),
		CourseStatus:      courseRequest.Status,
		CourseCertificate: courseRequest.Certify,
		LecturerID:        userID,
	}

	if err := db.DB.Create(&newCourse).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	return c.Status(fiber.StatusOK).JSON("Create Success!")
}

func GetCourses(c *fiber.Ctx) error {
	return nil
}

func GetCourseID(c *fiber.Ctx) error {
	return nil
}

func UpdateCourse(c *fiber.Ctx) error {
	return nil
}

func DeleteCourse(c *fiber.Ctx) error {
	return nil
}
